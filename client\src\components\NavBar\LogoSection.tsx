import React from 'react';
import { styled } from '@mui/material/styles';
import { Box, Typography, Avatar } from '@mui/material';
import { useTranslation } from 'react-i18next';

// Styled Components
const LogoSectionContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  cursor: 'pointer',
  transition: theme.transitions.create(['transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'scale(1.02)',
  },
}));

const LogoAvatar = styled(Avatar)(() => ({
  width: 32,
  height: 32,
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  border: '2px solid rgba(255, 255, 255, 0.3)',
  '& img': {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
  },
}));

const BrandText = styled(Typography)(({ theme }) => ({
  color: 'white',
  fontWeight: 600,
  fontSize: '1.1rem',
  [theme.breakpoints.down('sm')]: {
    fontSize: '1rem',
  },
}));

// Component Props Interface
interface LogoSectionProps {
  onClick?: () => void;
  logoSrc?: string;
  altText?: string;
}

const LogoSection: React.FC<LogoSectionProps> = ({ 
  onClick, 
  logoSrc = "/Pigeon Squad Logo.png", 
  altText = "Logo" 
}) => {
  const { t } = useTranslation();

  return (
    <LogoSectionContainer onClick={onClick}>
      <LogoAvatar>
        <img src={logoSrc} alt={altText} />
      </LogoAvatar>
      <BrandText variant="h6">
        {t('app.title')}
      </BrandText>
    </LogoSectionContainer>
  );
};

export default LogoSection;
