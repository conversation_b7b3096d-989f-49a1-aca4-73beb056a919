import React from 'react';
import { styled } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

// Styled Components
const LogoSectionContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  cursor: 'pointer',
  transition: theme.transitions.create(['transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'scale(1.02)',
  },
}));

const LogoImageContainer = styled(Box)(() => ({
  position: 'relative',
  width: 32,
  height: 32,
  overflow: 'hidden',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-10px',
    right: '-10px',
    height: '100%',
    background: 'linear-gradient(15deg, transparent 40%, rgba(135, 206, 250, 0.6) 45%, rgba(135, 206, 250, 0.8) 50%, rgba(135, 206, 250, 0.6) 55%, transparent 60%)',
    transform: 'skewX(-15deg)',
    pointerEvents: 'none',
    zIndex: 1,
  },
}));

const LogoImage = styled('img')(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'contain',
  position: 'relative',
  zIndex: 0,
}));

const BrandText = styled(Typography)(({ theme }) => ({
  color: 'white',
  fontWeight: 600,
  fontSize: '1.1rem',
  [theme.breakpoints.down('sm')]: {
    fontSize: '1rem',
  },
}));

// Component Props Interface
interface LogoSectionProps {
  onClick?: () => void;
  logoSrc?: string;
  altText?: string;
}

const LogoSection: React.FC<LogoSectionProps> = ({ 
  onClick, 
  logoSrc = "/Pigeon Squad Logo.png", 
  altText = "Logo" 
}) => {
  const { t } = useTranslation();

  return (
    <LogoSectionContainer onClick={onClick}>
      <LogoImageContainer>
        <LogoImage src={logoSrc} alt={altText} />
      </LogoImageContainer>
      <BrandText variant="h6">
        {t('app.title')}
      </BrandText>
    </LogoSectionContainer>
  );
};

export default LogoSection;
